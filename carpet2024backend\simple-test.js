// Simple test to debug the issue
const axios = require('axios');

console.log('🔍 Testing simple POST request...');

const simpleData = {
  invoiceNumber: 'RE-TEST',
  invoiceDate: new Date(),
  exporterRef: 'TEST-REF',
  goods: [
    {
      quality: 'Test Quality',
      design: 'Test Design',
      pieces: 1,
      quantitySqMeter: 1,
      rateFOB: 100,
      amountFOB: 100
    }
  ]
};

axios.post('http://localhost:2000/api/phase-four/export-invoice', simpleData, {
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 5000
})
.then(response => {
  console.log('✅ SUCCESS!');
  console.log('Response:', response.data);
})
.catch(error => {
  console.error('❌ ERROR:');
  if (error.response) {
    console.error('Status:', error.response.status);
    console.error('Data:', error.response.data);
  } else if (error.request) {
    console.error('No response received');
    console.error('Request:', error.request);
  } else {
    console.error('Error:', error.message);
  }
});
