// repositories/buyerRepository.js
const Buyer = require('../model/phase-3/buyer');

async function getAllBuyers() {
    // Optimize query for dropdown - only fetch required fields
    return await Buyer.find({}, {
        customerCode: 1,
        customerName: 1,
        customerAddress: 1,
        country: 1,
        zipCode: 1,
        _id: 1
    }).lean(); // Use lean() for faster queries
}

async function getBuyerById(id) {
    return await Buyer.findById(id);
}

async function createBuyer(buyerData) {
    const newBuyer = new Buyer(buyerData);
    return await newBuyer.save();
}

async function updateBuyer(id, newData) {
    return await Buyer.findByIdAndUpdate(id, newData, { new: true });
}

async function deleteBuyer(id) {
    return await Buyer.findByIdAndDelete(id);
}

module.exports = {
    getAllBuyers,
    getBuyerById,
    createBuyer,
    updateBuyer,
    deleteBuyer
};
