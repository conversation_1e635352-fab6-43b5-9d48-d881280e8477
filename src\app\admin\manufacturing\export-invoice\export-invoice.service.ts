import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, map, of } from 'rxjs';
import { environment } from '../../../../environments/environment.development';

export interface InvoiceData {
  id?: string;
  invoiceNumber: string;
  invoiceDate?: string; // Add invoice date field
  exporterRef: string;
  exporter: string;
  buyersOrderNoDate: string;
  buyersOrderNo?: string; // Add separate buyer order number field
  buyersOrderDate?: string; // Add separate buyer order date field
  otherReferences: string;
  consignee: string;
  buyerIfOther: string;
  preCarriageBy: string;
  placeOfReceipt: string;
  originCountry: string;
  destinationCountry: string;
  vesselNo: string;
  portOfLoading: string;
  portOfDischarge: string;
  finalDestination: string;
  marksAndNos: string;
  noOfKindOfPackage: string;
  descriptionOfGoods: string;
  goods: GoodsItem[];
  addedFreight: string;
  amountChargeableWords: string;
  signatureDate: string;
  signature?: string; // Add signature field for base64 image
  totalPieces: number;
  totalQuantity: number;
  totalCifEuro: string;
  grandTotal: string;
  area: string; // Add area (SizeMaster ObjectId)
  createdAt?: string;
  updatedAt?: string;
}

export interface GoodsItem {
  quality: string;
  design: string;
  pieces: number;
  quantity: number;
  rate: number;
  cifEuro: number;
  length?: number;
  width?: number;
  unit?: string;
  quantitySqMeter?: number;
  rateFOB?: number;
  amountFOB?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ExportInvoiceService {
  getAllBuyers() {
    throw new Error('Method not implemented.');
  }
  // This method is implemented below
  private apiUrl = `${environment.apiUrl}/phase-four/invoice`;
  private exportInvoiceApiUrl = `${environment.apiUrl}/phase-four/export-invoice`;
  private buyerOrderApiUrl = `${environment.apiUrl}/phase-four/buyerOrder`;
  private packingListApiUrl = `${environment.apiUrl}/phase-four/exportPackingList`;

  constructor(private http: HttpClient) { }

  // Get all buyer orders
  getAllBuyerOrders(): Observable<any[]> {
    return this.http.get<any[]>(`${this.buyerOrderApiUrl}/buyerOrder`).pipe(
      map(orders => {
        if (orders && Array.isArray(orders) && orders.length > 0) {
          return orders.map(order => ({
            id: order._id,
            orderNo: order.orderNo,
            buyerName: order.buyer ? order.buyer.name : '',
            orderDate: order.orderDate
          }));
        } else {
          console.log('API returned empty buyer orders list');
          return [
            { id: '1', orderNo: '100135', buyerName: 'Sample Buyer 1', orderDate: '09/03/2024' },
            { id: '2', orderNo: '100136', buyerName: 'Sample Buyer 2', orderDate: '10/03/2024' },
            { id: '3', orderNo: '100137', buyerName: 'Sample Buyer 3', orderDate: '11/03/2024' }
          ];
        }
      }),
      catchError(error => {
        console.error('Error fetching buyer orders:', error);
        return of([
          { id: '1', orderNo: '100135', buyerName: 'Sample Buyer 1', orderDate: '09/03/2024' },
          { id: '2', orderNo: '100136', buyerName: 'Sample Buyer 2', orderDate: '10/03/2024' },
          { id: '3', orderNo: '100137', buyerName: 'Sample Buyer 3', orderDate: '11/03/2024' }
        ]);
      })
    );
  }

  // Get all packing lists
  getAllPackingLists(): Observable<any[]> {
    return this.http.get<any>(`${this.packingListApiUrl}/exportPacking`).pipe(
      map(response => {
        // API se data property me aata hai
        if (response && response.success && Array.isArray(response.data)) {
          return response.data;
        }
        return [];
      }),
      catchError(error => {
        console.error('Error fetching packing lists:', error);
        return of([]);
      })
    );
  }

  // Get packing list by ID
  getPackingListById(id: string): Observable<any> {
    return this.http.get<any>(`${this.packingListApiUrl}/exportPacking/${id}`).pipe(
      map(response => {
        if (response && response.data) {
          return response.data;
        } else if (response) {
          return response;
        } else {
          console.log('API returned empty packing list');
          // Return sample data based on ID
          if (id === '1') {
            return {
              _id: '1',
              packingListNo: 'PL-001',
              packingDate: '15/03/2024',
              buyer: { name: 'Sample Buyer', address: 'Sample Address', country: 'Germany' },
              buyerOrder: { orderNo: '100135', orderDate: '09/03/2024' },
              items: [
                { quality: 'AMRAVATI', design: 'BHAKTIRI', pieces: 16, area: 58.71, length: 9.5, width: 6.2 },
                { quality: 'FARSI', design: 'BHAKTIRI', pieces: 7, area: 35.49, length: 8.3, width: 4.3 }
              ]
            };
          } else {
            return {
              _id: '2',
              packingListNo: 'PL-002',
              packingDate: '16/03/2024',
              buyer: { name: 'Sample Buyer 2', address: 'Sample Address 2', country: 'France' },
              buyerOrder: { orderNo: '100136', orderDate: '10/03/2024' },
              items: [
                { quality: 'SARANG', design: 'BHAKTIRI', pieces: 19, area: 80.27, length: 10.2, width: 7.9 },
                { quality: 'SHARTI', design: 'BHAKTIRI', pieces: 6, area: 26.82, length: 8.5, width: 3.15 }
              ]
            };
          }
        }
      }),
      catchError(error => {
        console.error('Error fetching packing list:', error);
        // Return sample data based on ID
        if (id === '1') {
          return of({
            _id: '1',
            packingListNo: 'PL-001',
            packingDate: '15/03/2024',
            buyer: { name: 'Sample Buyer', address: 'Sample Address', country: 'Germany' },
            buyerOrder: { orderNo: '100135', orderDate: '09/03/2024' },
            items: [
              { quality: 'AMRAVATI', design: 'BHAKTIRI', pieces: 16, area: 58.71, length: 9.5, width: 6.2 },
              { quality: 'FARSI', design: 'BHAKTIRI', pieces: 7, area: 35.49, length: 8.3, width: 4.3 }
            ]
          });
        } else {
          return of({
            _id: '2',
            packingListNo: 'PL-002',
            packingDate: '16/03/2024',
            buyer: { name: 'Sample Buyer 2', address: 'Sample Address 2', country: 'France' },
            buyerOrder: { orderNo: '100136', orderDate: '10/03/2024' },
            items: [
              { quality: 'SARANG', design: 'BHAKTIRI', pieces: 19, area: 80.27, length: 10.2, width: 7.9 },
              { quality: 'SHARTI', design: 'BHAKTIRI', pieces: 6, area: 26.82, length: 8.5, width: 3.15 }
            ]
          });
        }
      })
    );
  }

  // Get all invoices from exportinvoices collection
  getAllInvoices(): Observable<InvoiceData[]> {
    console.log('📡 Fetching invoices from exportinvoices collection...');
    console.log('📡 API URL:', `${this.exportInvoiceApiUrl}/getAll`);
    return this.http.get<any[]>(`${this.exportInvoiceApiUrl}/getAll`).pipe(
      map(data => {
        if (data && Array.isArray(data) && data.length > 0) {
          console.log('✅ Received invoices from exportinvoices collection:', data.length);
          // Transform the data to match the InvoiceData interface
          return data.map(invoice => ({
            id: invoice._id,
            invoiceNumber: invoice.invoiceNo || 'RE-755', // Just the invoice number
            invoiceDate: invoice.invoiceDate || '', // Add invoice date
            exporterRef: invoice.exporterRef || invoice.exportersRef || '',
            exporter: invoice.exporter ? `${invoice.exporter.name || ''}, ${invoice.exporter.address || ''}` : '',
            buyersOrderNoDate: `${invoice.buyerOrderNo || ''} Dt. ${invoice.buyerOrderDate || ''}`,
            buyersOrderNo: invoice.buyerOrderNo || '',
            buyersOrderDate: invoice.buyerOrderDate || '',
            otherReferences: invoice.otherReferences || '',
            consignee: invoice.consignee ? `${invoice.consignee.name || ''}, ${invoice.consignee.address || ''}, ${invoice.consignee.country || ''}` : '',
            buyerIfOther: invoice.buyerIfOther || 'SAME',
            preCarriageBy: invoice.preCarriageBy || (invoice.shippingDetails ? invoice.shippingDetails.preCarriageBy || '' : ''),
            placeOfReceipt: invoice.placeOfReceipt || (invoice.shippingDetails ? invoice.shippingDetails.placeOfReceipt || '' : ''),
            originCountry: invoice.originCountry || (invoice.shippingDetails ? invoice.shippingDetails.countryOfOrigin || 'INDIA' : 'INDIA'),
            destinationCountry: invoice.destinationCountry || (invoice.shippingDetails ? invoice.shippingDetails.countryOfDestination || '' : ''),
            vesselNo: invoice.vesselNo || (invoice.shippingDetails ? invoice.shippingDetails.vesselNo || '' : ''),
            portOfLoading: invoice.portOfLoading || (invoice.shippingDetails ? invoice.shippingDetails.portOfLoading || '' : ''),
            portOfDischarge: invoice.portOfDischarge || (invoice.shippingDetails ? invoice.shippingDetails.portOfDischarge || '' : ''),
            finalDestination: invoice.finalDestination || (invoice.shippingDetails ? invoice.shippingDetails.finalDestination || '' : ''),
            marksAndNos: invoice.marksAndNos || invoice.marksAndContNo || '',
            noOfKindOfPackage: invoice.noOfKindOfPackage || invoice.kindOfPkgs || '',
            descriptionOfGoods: invoice.goodsDescription || 'Indian Hand-Knotted Woolen Carpets',
            goods: (invoice.goods || invoice.goodsDetails) ? (invoice.goods || invoice.goodsDetails).map((item: any) => ({
              quality: item.quality || '',
              design: item.design || '',
              pieces: item.pieces || 0,
              quantity: item.quantitySqMeter || item.quantity || 0,
              rate: item.rateFOB || item.rate || 0,
              cifEuro: item.amountFOB || item.cifEuro || 0,
              length: item.length || 0,
              width: item.width || 0,
              unit: item.unit || 'sqft'
            })) : [],
            addedFreight: invoice.addedFreight ? invoice.addedFreight.toString() : '0.00',
            amountChargeableWords: invoice.amountInWords || '',
            signatureDate: '',
            signature: invoice.signature || '', // Include signature base64
            totalPieces: invoice.totalPieces || (invoice.goodsDetails ? invoice.goodsDetails.reduce((sum: number, item: any) => sum + (item.pieces || 0), 0) : 0),
            totalQuantity: invoice.totalQuantity || (invoice.goodsDetails ? invoice.goodsDetails.reduce((sum: number, item: any) => sum + (item.quantitySqMeter || 0), 0) : 0),
            totalCifEuro: invoice.totalCifEuro ? invoice.totalCifEuro.toString() : (invoice.totalFOB ? invoice.totalFOB.toString() : '0.00'),
            grandTotal: invoice.grandTotal ? invoice.grandTotal.toString() : (invoice.finalAmount ? invoice.finalAmount.toString() : '0.00'),
            area: invoice.area ? (typeof invoice.area === 'object' && invoice.area._id ? invoice.area._id : invoice.area) : '',
            createdAt: invoice.createdAt,
            updatedAt: invoice.updatedAt
          }));
        } else {
          console.log('❌ No invoices found in exportinvoices collection');
          return []; // Return empty array instead of sample data
        }
      }),
      catchError(error => {
        console.error('❌ Error fetching invoices from exportinvoices collection:', error);
        return of([]); // Return empty array instead of sample data
      })
    );
  }

  // Get invoice by ID from exportinvoices collection
  getInvoiceById(id: string): Observable<InvoiceData> {
    console.log('📡 Fetching invoice by ID from exportinvoices collection:', id);
    return this.http.get<any>(`${this.exportInvoiceApiUrl}/${id}`).pipe(
      map(invoice => {
        if (invoice) {
          console.log('✅ Received invoice from exportinvoices collection:', invoice.invoiceNo);
          console.log('🔍 Raw database invoice object:', JSON.stringify(invoice, null, 2));

          // Check specific fields in raw data
          console.log('🔍 Raw database fields:');
          console.log('  exportersRef:', invoice.exportersRef);
          console.log('  shippingDetails:', invoice.shippingDetails);
          console.log('  marksAndContNo:', invoice.marksAndContNo);
          console.log('  kindOfPkgs:', invoice.kindOfPkgs);
          console.log('  goodsDescription:', invoice.goodsDescription);
          console.log('  goodsDetails:', invoice.goodsDetails);

          // Transform the data to match the InvoiceData interface
          const transformedData = {
            id: invoice._id,
            invoiceNumber: invoice.invoiceNo || 'RE-755', // Just the invoice number
            invoiceDate: invoice.invoiceDate || '', // Add invoice date
            exporterRef: invoice.exporterRef || invoice.exportersRef || '',
            exporter: invoice.exporter ? `${invoice.exporter.name || ''}, ${invoice.exporter.address || ''}` : '',
            buyersOrderNoDate: `${invoice.buyerOrderNo || ''} Dt. ${invoice.buyerOrderDate || ''}`,
            buyersOrderNo: invoice.buyerOrderNo || '',
            buyersOrderDate: invoice.buyerOrderDate || '',
            otherReferences: invoice.otherReferences || '',
            consignee: invoice.consignee ? `${invoice.consignee.name || ''}, ${invoice.consignee.address || ''}, ${invoice.consignee.country || ''}` : '',
            buyerIfOther: invoice.buyerIfOther || 'SAME',
            preCarriageBy: invoice.preCarriageBy || (invoice.shippingDetails ? invoice.shippingDetails.preCarriageBy || '' : ''),
            placeOfReceipt: invoice.placeOfReceipt || (invoice.shippingDetails ? invoice.shippingDetails.placeOfReceipt || '' : ''),
            originCountry: invoice.originCountry || (invoice.shippingDetails ? invoice.shippingDetails.countryOfOrigin || 'INDIA' : 'INDIA'),
            destinationCountry: invoice.destinationCountry || (invoice.shippingDetails ? invoice.shippingDetails.countryOfDestination || '' : ''),
            vesselNo: invoice.vesselNo || (invoice.shippingDetails ? invoice.shippingDetails.vesselNo || '' : ''),
            portOfLoading: invoice.portOfLoading || (invoice.shippingDetails ? invoice.shippingDetails.portOfLoading || '' : ''),
            portOfDischarge: invoice.portOfDischarge || (invoice.shippingDetails ? invoice.shippingDetails.portOfDischarge || '' : ''),
            finalDestination: invoice.finalDestination || (invoice.shippingDetails ? invoice.shippingDetails.finalDestination || '' : ''),
            marksAndNos: invoice.marksAndNos || invoice.marksAndContNo || '',
            noOfKindOfPackage: invoice.noOfKindOfPackage || invoice.kindOfPkgs || '',
            descriptionOfGoods: invoice.goodsDescription || 'Indian Hand-Knotted Woolen Carpets',
            goods: (invoice.goods || invoice.goodsDetails) ? (invoice.goods || invoice.goodsDetails).map((item: any) => ({
              quality: item.quality || '',
              design: item.design || '',
              pieces: item.pieces || 0,
              quantity: item.quantitySqMeter || item.quantity || 0,
              rate: item.rateFOB || item.rate || 0,
              cifEuro: item.amountFOB || item.cifEuro || 0,
              length: item.length || 0,
              width: item.width || 0,
              unit: item.unit || 'sqft'
            })) : [],
            addedFreight: invoice.addedFreight ? invoice.addedFreight.toString() : '0.00',
            amountChargeableWords: invoice.amountInWords || '',
            signatureDate: '',
            signature: invoice.signature || '', // Include signature base64
            totalPieces: invoice.totalPieces || (invoice.goodsDetails ? invoice.goodsDetails.reduce((sum: number, item: any) => sum + (item.pieces || 0), 0) : 0),
            totalQuantity: invoice.totalQuantity || (invoice.goodsDetails ? invoice.goodsDetails.reduce((sum: number, item: any) => sum + (item.quantitySqMeter || 0), 0) : 0),
            totalCifEuro: invoice.totalCifEuro ? invoice.totalCifEuro.toString() : (invoice.totalFOB ? invoice.totalFOB.toString() : '0.00'),
            grandTotal: invoice.grandTotal ? invoice.grandTotal.toString() : (invoice.finalAmount ? invoice.finalAmount.toString() : '0.00'),
            area: invoice.area ? (typeof invoice.area === 'object' && invoice.area._id ? invoice.area._id : invoice.area) : '',
            createdAt: invoice.createdAt,
            updatedAt: invoice.updatedAt
          };

          console.log('🔄 Transformed data for frontend:', transformedData);
          console.log('🔄 Specific field check:');
          console.log('  exporterRef:', transformedData.exporterRef);
          console.log('  preCarriageBy:', transformedData.preCarriageBy);
          console.log('  vesselNo:', transformedData.vesselNo);
          console.log('  portOfLoading:', transformedData.portOfLoading);
          console.log('  marksAndNos:', transformedData.marksAndNos);
          console.log('  goods:', transformedData.goods);

          return transformedData;
        } else {
          console.log('❌ Invoice not found in exportinvoices collection');
          throw new Error('Invoice not found');
        }
      }),
      catchError(error => {
        console.error('❌ Error fetching invoice from exportinvoices collection:', error);
        throw error; // Re-throw error instead of returning sample data
      })
    );
  }

  // Create new invoice
  createInvoice(invoiceData: InvoiceData): Observable<InvoiceData> {
    try {
      // Prepare data in backend-expected structure
      const backendInvoice = this.prepareInvoiceForBackend(invoiceData);
      console.log('Sending invoice data to backend:', JSON.stringify(backendInvoice));
      console.log('API URL:', `${this.exportInvoiceApiUrl}`);

      // Use the export-invoice endpoint
      return this.http.post<any>(`${this.exportInvoiceApiUrl}`, backendInvoice, {
        headers: { 'Content-Type': 'application/json' }
      }).pipe(
        map(response => {
          console.log('Raw response from backend:', response);
          if (response && response._id) {
            console.log('Invoice saved to MongoDB successfully with _id:', response._id);
            return {
              ...invoiceData,
              id: response._id
            };
          } else if (response && response.insertedId) {
            console.log('Invoice saved to MongoDB successfully with insertedId:', response.insertedId);
            return {
              ...invoiceData,
              id: response.insertedId
            };
          } else if (response && response.id) {
            console.log('Invoice saved to MongoDB successfully with id:', response.id);
            return {
              ...invoiceData,
              id: response.id
            };
          } else {
            console.log('API returned empty or unexpected response format:', response);
            console.log('Using mock ID instead');
            return {
              ...invoiceData,
              id: this.generateMockId()
            };
          }
        }),
        catchError(error => {
          console.error('Error saving invoice:', error);
          console.error('Error details:', error.message);
          console.error('Error status:', error.status);
          console.error('Error URL:', error.url);

          if (error.error) {
            console.error('Server error details:', error.error);
          }

          // Try to log the request that failed
          try {
            console.error('Request that failed:', {
              url: this.exportInvoiceApiUrl,
              method: 'POST',
               body: JSON.stringify(backendInvoice)
            });
          } catch (e) {
            console.error('Could not log request details:', e);
          }

          return of({
            ...invoiceData,
            id: this.generateMockId()
          });
        })
      );
    } catch (error) {
      console.error('Exception in createInvoice method:', error);
      return of({
        ...invoiceData,
        id: this.generateMockId()
      });
    }
  }

  // Create invoice data for backend
  prepareInvoiceForBackend(invoiceData: InvoiceData): any {
    // Extract invoice number and date safely
    let invoiceNumberParts: string[] = [];
    if (invoiceData.invoiceNumber && typeof invoiceData.invoiceNumber === 'string') {
      invoiceNumberParts = invoiceData.invoiceNumber.split(' ');
    }
    const invoiceNo = invoiceNumberParts[0] || '';
    const invoiceDate = invoiceNumberParts.length > 1 ? invoiceNumberParts[1] : this.formatDate(new Date());

    // Extract consignee info safely (handles commas and newlines)
    let consigneeParts: string[] = [];
    let consigneeString = '';
    if (invoiceData.consignee && typeof invoiceData.consignee === 'object') {
      const c: any = invoiceData.consignee;
      consigneeString = [
        c.customerCode + ' - ' + c.customerName || c.name || '',
        c.customerAddress || c.address || '',
        c.country || '',
        c.zipCode || ''
      ].filter(Boolean).join(',\n');
    } else if (typeof invoiceData.consignee === 'string') {
      consigneeString = invoiceData.consignee;
    }
    if (consigneeString) {
      // Split by both comma and newline combinations
      consigneeParts = consigneeString.split(/,\n|,|\n/).map(part => part.trim()).filter(Boolean);
    }
    const consigneeAddress = consigneeParts.length > 1 ? consigneeParts[1] : '';
    const consigneeCountry = consigneeParts.length > 2 ? consigneeParts[2] : invoiceData.destinationCountry;

    // Extract buyer order info safely
    let buyerOrderParts: string[] = [];
    let buyerOrderNo = '';
    let buyerOrderDate = '';

    if (invoiceData.buyersOrderNoDate && typeof invoiceData.buyersOrderNoDate === 'string') {
      buyerOrderParts = invoiceData.buyersOrderNoDate.split(' Dt. ');
      buyerOrderNo = buyerOrderParts[0] || '';
      buyerOrderDate = buyerOrderParts.length > 1 ? buyerOrderParts[1] : '';
    } else {
      // Handle separate fields
      buyerOrderNo = invoiceData.buyersOrderNo || '';
      buyerOrderDate = invoiceData.buyersOrderDate || '';
    }

    // Format goods details
    const goodsDetails = invoiceData.goods.map(item => ({
      quality: item.quality,
      design: item.design,
      pieces: item.pieces !== undefined && item.pieces !== null ? parseInt(item.pieces.toString()) : 0,
      quantitySqMeter: item.quantity !== undefined && item.quantity !== null ? parseFloat(item.quantity.toString()) : 0,
      rateFOB: item.rate !== undefined && item.rate !== null ? parseFloat(item.rate.toString()) : 0,
      amountFOB: (item.quantity !== undefined && item.quantity !== null && item.rate !== undefined && item.rate !== null)
        ? parseFloat((Number(item.quantity) * Number(item.rate)).toFixed(2))
        : 0
    }));

    // Calculate totals
    const totalFOB = parseFloat(invoiceData.totalCifEuro);
    const freight = parseFloat(invoiceData.addedFreight || '0');
    const finalAmount = totalFOB + freight;

    // Create a properly formatted invoice object for MongoDB
    return {
      exporter: {
        name: 'M/S Rachin Exports',
        address: 'Madhosingh, Santravidas Nagar, Dist Bhadohi, U.P. India'
      },
      consignee: {
        name: consigneeParts[0] || 'DEFAULT CONSIGNEE', // Use actual consignee name
        address: consigneeAddress,
        country: consigneeCountry
      },
      invoiceNo: invoiceNo,
      invoiceDate: invoiceDate,
      exportersRef: invoiceData.exporterRef,
      buyerOrderNo: buyerOrderNo, // Use actual buyer order number
      buyerOrderDate: buyerOrderDate,
      gstNo: '09**********1ZJ', // Default value
      panNo: '**********', // Default value
      shippingDetails: {
        preCarriageBy: invoiceData.preCarriageBy,
        placeOfReceipt: invoiceData.placeOfReceipt,
        vesselNo: invoiceData.vesselNo,
        portOfLoading: invoiceData.portOfLoading,
        countryOfOrigin: invoiceData.originCountry,
        countryOfDestination: invoiceData.destinationCountry,
        portOfDischarge: invoiceData.portOfDischarge,
        finalDestination: invoiceData.finalDestination,
        deliveryTerms: ''
      },
      goodsDescription: invoiceData.descriptionOfGoods,
      marksAndContNo: invoiceData.marksAndNos,
      kindOfPkgs: invoiceData.noOfKindOfPackage,
      rollNumbers: '',
      area: invoiceData.area, // Use selected area ObjectId
      goodsDetails: goodsDetails,
      totalFOB: totalFOB,
      woolPercentage: 80,
      cottonPercentage: 20,
      grossWeight: 1092.00, // Default values
      netWeight: 1044.00,
      additionalCharges: {
        insurance: 0,
        igst: 0,
        igstPercentage: 0
      },
      finalAmount: finalAmount,
      amountInWords: invoiceData.amountChargeableWords,
      signature: invoiceData.signature || '', // Add signature base64 data
      rexRegistrationNo: 'INREX1502000776DG015',
      declaration: "We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct."
    };
  }

  // Update invoice
  updateInvoice(id: string, invoiceData: InvoiceData): Observable<InvoiceData> {
    return new Observable<InvoiceData>(observer => {
      this.http.put<InvoiceData>(`${this.apiUrl}/${id}`, invoiceData).subscribe({
        next: (data) => {
          if (data) {
            observer.next(data);
          } else {
            // Return mock response if API returns empty
            console.log('API returned empty response for update, using input data');
            observer.next(invoiceData);
          }
          observer.complete();
        },
        error: (error) => {
          console.error('Error updating invoice:', error);
          // Return input data on error
          observer.next(invoiceData);
          observer.complete();
        }
      });
    });
  }

  // Delete invoice
  deleteInvoice(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  // Generate invoice number
  generateInvoiceNumber(): Observable<{ invoiceNumber: string }> {
    console.log('🔢 Calling backend to generate invoice number...');

    // Call backend API to generate invoice number
    return this.http.get<{ invoiceNumber: string }>(`${this.exportInvoiceApiUrl}/generate-number`).pipe(
      map(response => {
        console.log('✅ Backend response:', response);
        return {
          invoiceNumber: response.invoiceNumber // Only return RE-XXX without date
        };
      }),
      catchError(error => {
        console.error('❌ Error generating invoice number from backend:', error);
        // Fallback to local generation
        const nextNumber = '755';
        const invoiceNumber = `RE-${nextNumber}`;
        console.log('🔄 Using fallback invoice number:', invoiceNumber);

        return of({
          invoiceNumber: invoiceNumber
        });
      })
    );

    /*
    return new Observable<{ invoiceNumber: string }>(observer => {
      this.http.get<{ invoiceNumber: string }>(`${this.exportInvoiceApiUrl}/generate-number`).subscribe({
        next: (data) => {
          if (data && data.invoiceNumber) {
            observer.next(data);
          } else {
            // Generate mock invoice number if API returns empty
            console.log('API returned empty invoice number, generating mock number');
            const nextNumber = this.getNextInvoiceNumber();
            observer.next({
              invoiceNumber: `RE-${nextNumber}`
            });
          }
          observer.complete();
        },
        error: (error) => {
          console.error('Error generating invoice number:', error);
          // Generate mock invoice number on error
          const nextNumber = this.getNextInvoiceNumber();
          observer.next({
            invoiceNumber: `RE-${nextNumber}`
          });
          observer.complete();
        }
      });
    });
    */
  }

  // Cache for consignees to avoid repeated API calls
  private consigneesCache: any[] | null = null;
  private consigneesCacheTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Get all consignees with caching
  getAllConsignees(): Observable<any[]> {
    // Check if we have valid cached data
    const now = Date.now();
    if (this.consigneesCache && (now - this.consigneesCacheTime) < this.CACHE_DURATION) {
      console.log('🚀 Using cached consignees data');
      return of(this.consigneesCache);
    }

    console.log('📡 Fetching fresh consignees data...');
    return this.http.get<any[]>(`${environment.apiUrl}/phase-four/buyer`).pipe(
      map(consignees => {
        const result = Array.isArray(consignees) ? consignees : [];
        // Cache the result
        this.consigneesCache = result;
        this.consigneesCacheTime = now;
        console.log('✅ Consignees cached successfully');
        return result;
      }),
      catchError(error => {
        console.error('❌ Error fetching consignees:', error);
        // Return cached data if available, otherwise empty array
        return of(this.consigneesCache || []);
      })
    );
  }

  // Get the next invoice number based on existing invoices
  private getNextInvoiceNumber(): string {
    try {
      // Try to get the highest invoice number from existing invoices
      const sampleInvoices = this.getSampleInvoices();
      let highestNumber = 0;

      sampleInvoices.forEach(invoice => {
        const match = invoice.invoiceNumber.match(/RE-(\d+)/);
        if (match && match[1]) {
          const num = parseInt(match[1], 10);
          if (!isNaN(num) && num > highestNumber) {
            highestNumber = num;
          }
        }
      });

      // Return the next number, starting from 755 if no invoices exist
      return (highestNumber > 0 ? highestNumber + 1 : 755).toString().padStart(3, '0');
    } catch (error) {
      console.error('Error getting next invoice number:', error);
      // Fallback to 001 instead of random number
      return '001';
    }
  }

  // Helper methods for generating sample data
  private getSampleInvoices(): InvoiceData[] {
    return [
      {
        id: '1',
        invoiceNumber: 'RE-659 11/03/2024',
        exporterRef: 'TYPE-1',
        exporter: 'M/S. RACHIN EXPORTS, SUREKA ESTATE, MAHANTH SHIWALA, MIRZAPUR, U.P., INDIA',
        buyersOrderNoDate: '100135 Dt. 09/03/2024',
        otherReferences: 'SYPB',
        consignee: 'M/S KESHARI ORIENT TEPPICHE IMPORT GmbH, STOCKUM 2A, D-49653 COSEFELD, GERMANY',
        buyerIfOther: 'SAME',
        preCarriageBy: 'LORRY',
        placeOfReceipt: 'MADHOSHIGH',
        originCountry: 'INDIA',
        destinationCountry: 'GERMANY',
        vesselNo: 'BY SEA',
        portOfLoading: 'MUMBAI',
        portOfDischarge: 'ROTTERDAM',
        finalDestination: 'ROTTERDAM',
        marksAndNos: 'RE/KOП\nROTTERDAM\nRoll No. 388-405,468-465,491-492,525-540',
        noOfKindOfPackage: '55 Roll/Bale',
        descriptionOfGoods: 'Indian Hand-Knotted Woolen Carpets',
        goods: [
          { quality: 'AMRAVATI', design: 'BHAKTIRI', pieces: 16, quantity: 58.71, rate: 141.00, cifEuro: 8277.11, length: 9.5, width: 6.2, unit: 'sqft' },
          { quality: 'FARSI', design: 'BHAKTIRI', pieces: 7, quantity: 35.49, rate: 141.00, cifEuro: 5004.09, length: 8.3, width: 4.3, unit: 'sqft' },
          { quality: 'MUDRA', design: 'BHAKTIRI', pieces: 7, quantity: 29.77, rate: 141.00, cifEuro: 4197.57, length: 7.1, width: 4.2, unit: 'sqft' },
        ],
        addedFreight: '0.00',
        amountChargeableWords: 'EURO TWENTY SEVEN THOUSAND SEVEN HUNDRED SIXTY THREE AND POINT TWENTY SIX ONLY',
        signatureDate: '',
        totalPieces: 30,
        totalQuantity: 123.97,
        totalCifEuro: '17478.77',
        grandTotal: '17478.77',
        area: '60' // Sample area
      },
      {
        id: '2',
        invoiceNumber: 'RE-660 12/03/2024',
        exporterRef: 'TYPE-2',
        exporter: 'M/S. RACHIN EXPORTS, SUREKA ESTATE, MAHANTH SHIWALA, MIRZAPUR, U.P., INDIA',
        buyersOrderNoDate: '100136 Dt. 10/03/2024',
        otherReferences: 'SYPB',
        consignee: 'M/S KESHARI ORIENT TEPPICHE IMPORT GmbH, STOCKUM 2A, D-49653 COSEFELD, GERMANY',
        buyerIfOther: 'SAME',
        preCarriageBy: 'LORRY',
        placeOfReceipt: 'MADHOSHIGH',
        originCountry: 'INDIA',
        destinationCountry: 'GERMANY',
        vesselNo: 'BY SEA',
        portOfLoading: 'MUMBAI',
        portOfDischarge: 'ROTTERDAM',
        finalDestination: 'ROTTERDAM',
        marksAndNos: 'RE/KOП\nROTTERDAM\nRoll No. 406-420,466-480',
        noOfKindOfPackage: '30 Roll/Bale',
        descriptionOfGoods: 'Indian Hand-Knotted Woolen Carpets',
        goods: [
          { quality: 'SARANG', design: 'BHAKTIRI', pieces: 19, quantity: 80.27, rate: 81.00, cifEuro: 6501.87, length: 10.2, width: 7.9, unit: 'sqft' },
          { quality: 'SHARTI', design: 'BHAKTIRI', pieces: 6, quantity: 26.82, rate: 141.00, cifEuro: 3781.62, length: 8.5, width: 3.15, unit: 'sqft' },
        ],
        addedFreight: '0.00',
        amountChargeableWords: 'EURO TEN THOUSAND TWO HUNDRED EIGHTY THREE AND POINT FORTY NINE ONLY',
        signatureDate: '',
        totalPieces: 25,
        totalQuantity: 107.09,
        totalCifEuro: '10283.49',
        grandTotal: '10283.49',
        area: '30' // Sample area
      }
    ];
  }

  private generateMockId(): string {
    return Math.floor(Math.random() * 1000).toString();
  }

  // Not used anymore, but kept for reference
  private generateMockInvoiceNumber(): string {
    return '001'; // Always start with 001
  }

  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}/${month}/${year}`;
  }

  // Prepare data for MongoDB
  public prepareMongoDbData(invoiceData: InvoiceData): any {
    // Extract invoice number and date
    const invoiceNumberParts = invoiceData.invoiceNumber.split(' ');
    const invoiceNo = invoiceNumberParts[0];
    const invoiceDate = invoiceNumberParts.length > 1 ? invoiceNumberParts[1] : this.formatDate(new Date());

    // Format goods details - use the correct field names to match MongoDB schema
    const goodsDetails = invoiceData.goods.map(item => ({
      quality: item.quality,
      design: item.design,
      pieces: parseInt(item.pieces.toString()),
      quantity: parseFloat(item.quantity.toString()),
      rate: parseFloat(item.rate.toString()),
      amount: parseFloat((item.quantity * item.rate).toFixed(2))
    }));

    // Calculate totals
    const totalAmount = parseFloat(invoiceData.totalCifEuro);
    const freight = parseFloat(invoiceData.addedFreight || '0');
    const grandTotal = totalAmount + freight;

    // Create a simplified invoice object for MongoDB
    return {
      invoiceNumber: invoiceData.invoiceNumber,
      date: invoiceDate,
      buyersOrderNoDate: invoiceData.buyersOrderNoDate,
      exporterRef: invoiceData.exporterRef,
      exporter: invoiceData.exporter,
      consignee: invoiceData.consignee,
      preCarriageBy: invoiceData.preCarriageBy,
      placeOfReceipt: invoiceData.placeOfReceipt,
      vesselNo: invoiceData.vesselNo,
      portOfLoading: invoiceData.portOfLoading,
      portOfDischarge: invoiceData.portOfDischarge,
      finalDestination: invoiceData.finalDestination,
      originCountry: invoiceData.originCountry,
      destinationCountry: invoiceData.destinationCountry,
      marksAndNos: invoiceData.marksAndNos,
      noOfKindOfPackage: invoiceData.noOfKindOfPackage,
      descriptionOfGoods: invoiceData.descriptionOfGoods,
      goods: goodsDetails,
      totalPieces: invoiceData.totalPieces,
      totalQuantity: invoiceData.totalQuantity,
      totalCifEuro: invoiceData.totalCifEuro,
      addedFreight: invoiceData.addedFreight,
      grandTotal: grandTotal.toFixed(2),
      amountChargeableWords: invoiceData.amountChargeableWords,
      // Add database and collection info to ensure it goes to the right place
      database: "test",
      collection: "invoices"
    };
  }
}
