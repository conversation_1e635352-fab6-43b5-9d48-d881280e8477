const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const { Local_PORT } = require('./config/serverConfig');
const appRouter = require('./routes/index');
const connect = require('./config/dbConfig');
const mongoose = require('mongoose');
const app = express();

// Allow all origins (for development)
app.use(cors());

// ...ya agar aap specific origin allow karna chahte hain:
app.use(cors({
  origin: 'http://localhost:4200', // Angular dev server ka default port
  credentials: true
}));

app.use(helmet()); // Set security-related HTTP headers
app.use(morgan('dev')); // Log HTTP requests

app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());

// Serve static files from the public directory
app.use('/public', express.static(path.join(__dirname, '../public'))); // Adjust the path if necessary


// Serve static files from the "public/all-files" directory when accessing "/images"
app.use('/images', express.static(path.join(__dirname, '../public/all-files')));

// Mount the application routes
app.use('/api', appRouter);

// Serve the main application
const allowedExt = [
  ".js", ".ico", ".css", ".png", ".jpg", ".gif", ".woff2", ".woff", ".ttf", ".svg", ".mp4",
];

app.get("**", (req, res) => {
  console.log(req.url);
  if (allowedExt.some(ext => req.url.includes(ext))) {
    console.log("allowext");
    let url = req.url.split("?")[0];
    res.sendFile(path.resolve(path.join(__dirname, "dist", url)));
  } else {
    console.log("else");
    res.sendFile(path.resolve(path.join(__dirname, "dist", "index.html")));
  }
});

// Set strictPopulate to false for Mongoose globally
mongoose.set('strictPopulate', false);
// Start the server
app.listen(Local_PORT, () => {
  console.log(`✅ Server started running on port ${Local_PORT}`);

  // Connect to database (non-blocking)
  connect()
    .then(() => console.log('✅ Successfully connected to MongoDB'))
    .catch((err) => {
      console.log('⚠️ MongoDB connection failed, but server is running:', err.message);
      console.log('📝 You can still test the API endpoints');
    });
});
