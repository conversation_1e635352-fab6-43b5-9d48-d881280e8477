const invoiceService = require('../../services/manifacturing/invoice-service');
const Invoice = require('../../model/phase-4/invoice');
const FrontendInvoice = require('../../model/phase-4/frontend-invoice');

class ExportInvoiceController {
  // Get all invoices in the format needed for export-invoice component
  async getAllExportInvoices(req, res) {
    try {
      // Get all invoices from the regular invoice model
      const invoices = await invoiceService.getAllInvoices();
      
      // Transform the invoices to match the export-invoice component format
      const transformedInvoices = invoices.map(invoice => {
        return {
          id: invoice._id,
          invoiceNumber: `${invoice.invoiceNo} ${invoice.invoiceDate}`,
          date: invoice.invoiceDate
        };
      });
      
      res.status(200).json(transformedInvoices);
    } catch (error) {
      console.error('Error fetching export invoices:', error);
      res.status(500).json({ message: 'Error fetching export invoices', error: error.message });
    }
  }

  // Get invoice by ID in the format needed for export-invoice component
  async getExportInvoiceById(req, res) {
    try {
      const { id } = req.params;
      
      // Get invoice from the regular Invoice collection
      const invoice = await invoiceService.getInvoiceById(id);
      
      if (!invoice) {
        return res.status(404).json({ message: 'Invoice not found' });
      }
      
      // Transform the invoice to match the export-invoice component format
      const transformedInvoice = {
        id: invoice._id,
        invoiceNumber: invoice.invoiceNo,
        invoiceDate: invoice.invoiceDate,
        exporterRef: invoice.exportersRef || 'TYPE-1',
        exporter: `${invoice.exporter.name}, ${invoice.exporter.address}`,
        buyersOrderNoDate: `${invoice.buyerOrderNo ? invoice.buyerOrderNo.orderNo : ''} Dt. ${invoice.buyerOrderDate || ''}`,
        otherReferences: '',
        consignee: invoice.consignee ? `${invoice.consignee.name.name || ''}, ${invoice.consignee.address || ''}, ${invoice.consignee.country || ''}` : '',
        buyerIfOther: 'SAME',
        preCarriageBy: invoice.shippingDetails ? invoice.shippingDetails.preCarriageBy || '' : '',
        placeOfReceipt: invoice.shippingDetails ? invoice.shippingDetails.placeOfReceipt || '' : '',
        originCountry: invoice.shippingDetails ? invoice.shippingDetails.countryOfOrigin || 'INDIA' : 'INDIA',
        destinationCountry: invoice.shippingDetails ? invoice.shippingDetails.countryOfDestination || '' : '',
        vesselNo: invoice.shippingDetails ? invoice.shippingDetails.vesselNo || '' : '',
        portOfLoading: invoice.shippingDetails ? invoice.shippingDetails.portOfLoading || '' : '',
        portOfDischarge: invoice.shippingDetails ? invoice.shippingDetails.portOfDischarge || '' : '',
        finalDestination: invoice.shippingDetails ? invoice.shippingDetails.finalDestination || '' : '',
        marksAndNos: invoice.marksAndContNo || '',
        noOfKindOfPackage: invoice.kindOfPkgs || '',
        descriptionOfGoods: invoice.goodsDescription || 'Indian Hand-Knotted Woolen Carpets',
        goods: invoice.goodsDetails ? invoice.goodsDetails.map(item => ({
          quality: item.quality || '',
          design: item.design || '',
          pieces: item.pieces || 0,
          quantity: item.quantitySqMeter || 0,
          rate: item.rateFOB || 0,
          cifEuro: item.amountFOB || 0,
          length: 0, // Default values as these might not be in the original model
          width: 0,
          unit: 'sqft'
        })) : [],
        addedFreight: '0.00',
        amountChargeableWords: invoice.amountInWords || '',
        signatureDate: '',
        totalPieces: invoice.goodsDetails ? invoice.goodsDetails.reduce((sum, item) => sum + (item.pieces || 0), 0) : 0,
        totalQuantity: invoice.goodsDetails ? invoice.goodsDetails.reduce((sum, item) => sum + (item.quantitySqMeter || 0), 0) : 0,
        totalCifEuro: invoice.totalFOB ? invoice.totalFOB.toString() : '0.00',
        grandTotal: invoice.finalAmount ? invoice.finalAmount.toString() : '0.00'
      };
      
      res.status(200).json(transformedInvoice);
    } catch (error) {
      console.error('Error fetching export invoice:', error);
      res.status(500).json({ message: 'Error fetching export invoice', error: error.message });
    }
  }

  // Create a new export invoice
  async createExportInvoice(req, res) {
    try {
      console.log('🚀 === INVOICE CREATION STARTED ===');
      console.log('📥 Received invoice creation request');
      console.log('📄 Request body:', JSON.stringify(req.body, null, 2));

      const invoiceData = req.body;

      // Generate auto-increment invoice number
      const latestInvoice = await FrontendInvoice.findOne().sort({ createdAt: -1 });
      let nextNumber = 755; // Starting number

      if (latestInvoice && latestInvoice.invoiceNo) {
        const match = latestInvoice.invoiceNo.match(/RE-(\d+)/);
        if (match && match[1]) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }

      const autoInvoiceNumber = `RE-${nextNumber}`;
      console.log('🔢 Generated invoice number:', autoInvoiceNumber);

      // Create FrontendInvoice document from incoming data
      const frontendInvoiceData = {
        invoiceNo: autoInvoiceNumber,
        invoiceDate: invoiceData.invoiceDate || new Date(),
        exporterRef: invoiceData.exportersRef || invoiceData.exporterRef || 'DEFAULT-REF',
        exporter: invoiceData.exporter || 'M/S. RACHIN EXPORTS',
        buyersOrderNo: invoiceData.buyerOrderNo || invoiceData.buyersOrderNo || 'DEFAULT-ORDER',
        buyersOrderDate: invoiceData.buyerOrderDate || invoiceData.buyersOrderDate || new Date(),
        otherReferences: invoiceData.otherReferences || '',
        consignee: invoiceData.consignee || 'DEFAULT CONSIGNEE',
        buyerIfOther: invoiceData.buyerIfOther || 'SAME',
        preCarriageBy: invoiceData.shippingDetails?.preCarriageBy || invoiceData.preCarriageBy || '',
        placeOfReceipt: invoiceData.shippingDetails?.placeOfReceipt || invoiceData.placeOfReceipt || '',
        originCountry: invoiceData.shippingDetails?.countryOfOrigin || invoiceData.originCountry || 'INDIA',
        destinationCountry: invoiceData.shippingDetails?.countryOfDestination || invoiceData.destinationCountry || 'Germany',
        vesselNo: invoiceData.shippingDetails?.vesselNo || invoiceData.vesselNo || '',
        portOfLoading: invoiceData.shippingDetails?.portOfLoading || invoiceData.portOfLoading || '',
        portOfDischarge: invoiceData.shippingDetails?.portOfDischarge || invoiceData.portOfDischarge || 'Hamburg',
        finalDestination: invoiceData.shippingDetails?.finalDestination || invoiceData.finalDestination || 'Germany',
        marksAndNos: invoiceData.marksAndContNo || invoiceData.marksAndNos || '',
        noOfKindOfPackage: invoiceData.kindOfPkgs || invoiceData.noOfKindOfPackage || '',
        descriptionOfGoods: invoiceData.goodsDescription || invoiceData.descriptionOfGoods || 'Indian Hand-Knotted Woolen Carpets',
        area: invoiceData.area || '507f1f77bcf86cd799439011',
        goods: Array.isArray(invoiceData.goodsDetails) ? invoiceData.goodsDetails :
               Array.isArray(invoiceData.goods) ? invoiceData.goods : [{
          quality: 'Default Quality',
          design: 'Default Design',
          pieces: 1,
          quantitySqMeter: 1,
          rateFOB: 100,
          amountFOB: 100
        }],
        addedFreight: parseFloat(invoiceData.addedFreight) || 0,
        insurance: parseFloat(invoiceData.additionalCharges?.insurance || invoiceData.insurance) || 0,
        igstPercentage: parseFloat(invoiceData.additionalCharges?.igstPercentage || invoiceData.igstPercentage) || 0,
        igst: parseFloat(invoiceData.additionalCharges?.igst || invoiceData.igst) || 0,
        amountChargeableWords: invoiceData.amountInWords || invoiceData.amountChargeableWords || 'One Hundred Only',
        signature: invoiceData.signature || '',

        // Use totals from incoming data or calculate
        totalPieces: invoiceData.totalPieces ||
          (Array.isArray(invoiceData.goodsDetails)
            ? invoiceData.goodsDetails.reduce((sum, item) => sum + (parseInt(item.pieces) || 0), 0)
            : 1),
        totalQuantity: invoiceData.totalQuantity ||
          (Array.isArray(invoiceData.goodsDetails)
            ? invoiceData.goodsDetails.reduce((sum, item) => sum + (parseFloat(item.quantitySqMeter) || 0), 0)
            : 1),
        totalCifEuro: parseFloat(invoiceData.totalFOB) || parseFloat(invoiceData.totalCifEuro) || 100,
        grandTotal: parseFloat(invoiceData.finalAmount) || parseFloat(invoiceData.grandTotal) || 100
      };

      console.log('💾 Saving to MongoDB:', JSON.stringify(frontendInvoiceData, null, 2));

      // Save to FrontendInvoice collection
      const createdInvoice = new FrontendInvoice(frontendInvoiceData);
      const savedInvoice = await createdInvoice.save();

      console.log('✅ Invoice saved successfully:', savedInvoice._id);
      console.log('🔢 Invoice number:', savedInvoice.invoiceNo);

      res.status(201).json({
        id: savedInvoice._id,
        invoiceNumber: savedInvoice.invoiceNo,
        message: 'Invoice created successfully',
        success: true
      });

    } catch (error) {
      console.error('❌ Error creating export invoice:', error);
      console.error('Stack trace:', error.stack);
      res.status(500).json({
        message: 'Error creating export invoice',
        error: error.message,
        success: false
      });
    }
  }

  // Generate invoice number
  async generateInvoiceNumber(req, res) {
    try {
      console.log('🔢 Generating invoice number...');

      // Get the latest invoice to determine the next number
      const latestInvoice = await FrontendInvoice.findOne().sort({ createdAt: -1 });

      let nextNumber = 755; // Default starting number

      if (latestInvoice && latestInvoice.invoiceNo) {
        console.log('📄 Latest invoice found:', latestInvoice.invoiceNo);
        // Extract the number part from the latest invoice number (format: RE-XXX)
        const match = latestInvoice.invoiceNo.match(/RE-(\d+)/);
        if (match && match[1]) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }

      // Format the invoice number
      const invoiceNumber = `RE-${nextNumber}`;
      console.log('✅ Generated invoice number:', invoiceNumber);

      res.status(200).json({ invoiceNumber });
    } catch (error) {
      console.error('❌ Error generating invoice number:', error);
      res.status(500).json({ message: 'Error generating invoice number', error: error.message });
    }
  }
}

module.exports = new ExportInvoiceController();
