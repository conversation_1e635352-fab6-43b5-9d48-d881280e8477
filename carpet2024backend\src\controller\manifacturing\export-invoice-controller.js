const invoiceService = require('../../services/manifacturing/invoice-service');
const Invoice = require('../../model/phase-4/invoice');
const FrontendInvoice = require('../../model/phase-4/frontend-invoice');

// Standalone function to generate next invoice number
async function generateNextInvoiceNumber() {
  try {
    // Find the latest invoice
    const latestInvoice = await FrontendInvoice.findOne().sort({ createdAt: -1 });

    let nextNumber = 755; // Starting number

    if (latestInvoice && latestInvoice.invoiceNo) {
      // Extract number from RE-XXX format
      const match = latestInvoice.invoiceNo.match(/RE-(\d+)/);
      if (match && match[1]) {
        nextNumber = parseInt(match[1], 10) + 1;
      }
    }

    return `RE-${nextNumber}`;
  } catch (error) {
    console.error('Error generating invoice number:', error);
    return `RE-${Date.now()}`; // Fallback
  }
}

class ExportInvoiceController {
  // Get all invoices in the format needed for export-invoice component
  async getAllExportInvoices(req, res) {
    try {
      // Get all invoices from the regular invoice model
      const invoices = await invoiceService.getAllInvoices();
      
      // Transform the invoices to match the export-invoice component format
      const transformedInvoices = invoices.map(invoice => {
        return {
          id: invoice._id,
          invoiceNumber: `${invoice.invoiceNo} ${invoice.invoiceDate}`,
          date: invoice.invoiceDate
        };
      });
      
      res.status(200).json(transformedInvoices);
    } catch (error) {
      console.error('Error fetching export invoices:', error);
      res.status(500).json({ message: 'Error fetching export invoices', error: error.message });
    }
  }

  // Get invoice by ID in the format needed for export-invoice component
  async getExportInvoiceById(req, res) {
    try {
      const { id } = req.params;
      
      // Get invoice from the regular Invoice collection
      const invoice = await invoiceService.getInvoiceById(id);
      
      if (!invoice) {
        return res.status(404).json({ message: 'Invoice not found' });
      }
      
      // Transform the invoice to match the export-invoice component format
      const transformedInvoice = {
        id: invoice._id,
        invoiceNumber: invoice.invoiceNo,
        invoiceDate: invoice.invoiceDate,
        exporterRef: invoice.exportersRef || 'TYPE-1',
        exporter: `${invoice.exporter.name}, ${invoice.exporter.address}`,
        buyersOrderNoDate: `${invoice.buyerOrderNo ? invoice.buyerOrderNo.orderNo : ''} Dt. ${invoice.buyerOrderDate || ''}`,
        otherReferences: '',
        consignee: invoice.consignee ? `${invoice.consignee.name.name || ''}, ${invoice.consignee.address || ''}, ${invoice.consignee.country || ''}` : '',
        buyerIfOther: 'SAME',
        preCarriageBy: invoice.shippingDetails ? invoice.shippingDetails.preCarriageBy || '' : '',
        placeOfReceipt: invoice.shippingDetails ? invoice.shippingDetails.placeOfReceipt || '' : '',
        originCountry: invoice.shippingDetails ? invoice.shippingDetails.countryOfOrigin || 'INDIA' : 'INDIA',
        destinationCountry: invoice.shippingDetails ? invoice.shippingDetails.countryOfDestination || '' : '',
        vesselNo: invoice.shippingDetails ? invoice.shippingDetails.vesselNo || '' : '',
        portOfLoading: invoice.shippingDetails ? invoice.shippingDetails.portOfLoading || '' : '',
        portOfDischarge: invoice.shippingDetails ? invoice.shippingDetails.portOfDischarge || '' : '',
        finalDestination: invoice.shippingDetails ? invoice.shippingDetails.finalDestination || '' : '',
        marksAndNos: invoice.marksAndContNo || '',
        noOfKindOfPackage: invoice.kindOfPkgs || '',
        descriptionOfGoods: invoice.goodsDescription || 'Indian Hand-Knotted Woolen Carpets',
        goods: invoice.goodsDetails ? invoice.goodsDetails.map(item => ({
          quality: item.quality || '',
          design: item.design || '',
          pieces: item.pieces || 0,
          quantity: item.quantitySqMeter || 0,
          rate: item.rateFOB || 0,
          cifEuro: item.amountFOB || 0,
          length: 0, // Default values as these might not be in the original model
          width: 0,
          unit: 'sqft'
        })) : [],
        addedFreight: '0.00',
        amountChargeableWords: invoice.amountInWords || '',
        signatureDate: '',
        totalPieces: invoice.goodsDetails ? invoice.goodsDetails.reduce((sum, item) => sum + (item.pieces || 0), 0) : 0,
        totalQuantity: invoice.goodsDetails ? invoice.goodsDetails.reduce((sum, item) => sum + (item.quantitySqMeter || 0), 0) : 0,
        totalCifEuro: invoice.totalFOB ? invoice.totalFOB.toString() : '0.00',
        grandTotal: invoice.finalAmount ? invoice.finalAmount.toString() : '0.00'
      };
      
      res.status(200).json(transformedInvoice);
    } catch (error) {
      console.error('Error fetching export invoice:', error);
      res.status(500).json({ message: 'Error fetching export invoice', error: error.message });
    }
  }

  // Create a new export invoice - Frontend ke exact structure ke hisaab se
  async createExportInvoice(req, res) {
    console.log('🚀 Invoice creation started...');
    try {
      console.log('=== FRONTEND TO BACKEND INVOICE CREATION ===');
      console.log('Received frontend data:', JSON.stringify(req.body, null, 2));

      const frontendData = req.body;

      // Get next invoice number automatically
      const nextInvoiceNumber = await generateNextInvoiceNumber();

      // Handle data from frontend service (prepareInvoiceForBackend method)
      const invoiceData = {
        // Basic invoice info - auto-generated invoice number
        invoiceNo: nextInvoiceNumber, // Auto-generated
        invoiceDate: frontendData.invoiceDate || new Date().toLocaleDateString('en-GB'),
        exportersRef: frontendData.exportersRef || 'DEFAULT-REF',

        // Exporter info - from frontend service
        exporter: frontendData.exporter?.name || 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',

        // Buyer/Consignee info - from frontend service structure
        buyersOrderNo: frontendData.buyerOrderNo || 'DEFAULT-ORDER',
        buyersOrderDate: frontendData.buyerOrderDate || new Date().toLocaleDateString('en-GB'),
        otherReferences: frontendData.otherReferences || '',
        consignee: frontendData.consignee?.address || frontendData.consignee || 'DEFAULT CONSIGNEE',
        buyerIfOther: 'SAME',

        // Shipping details - from frontend service shippingDetails object
        preCarriageBy: frontendData.shippingDetails?.preCarriageBy || '',
        placeOfReceipt: frontendData.shippingDetails?.placeOfReceipt || '',
        originCountry: frontendData.shippingDetails?.countryOfOrigin || 'INDIA',
        destinationCountry: frontendData.shippingDetails?.countryOfDestination || '',
        vesselNo: frontendData.shippingDetails?.vesselNo || '',
        portOfLoading: frontendData.shippingDetails?.portOfLoading || '',
        portOfDischarge: frontendData.shippingDetails?.portOfDischarge || '',
        finalDestination: frontendData.shippingDetails?.finalDestination || '',

        // Goods info - from frontend service
        marksAndNos: frontendData.marksAndContNo || '',
        noOfKindOfPackage: frontendData.kindOfPkgs || '',
        descriptionOfGoods: frontendData.goodsDescription || 'Indian Hand-Knotted Woolen Carpets',
        area: frontendData.area || null,

        // Goods array - from frontend service goodsDetails
        goods: Array.isArray(frontendData.goodsDetails) ? frontendData.goodsDetails.map(item => ({
          quality: item.quality || '',
          design: item.design || '',
          pieces: parseInt(item.pieces) || 0,
          quantitySqMeter: parseFloat(item.quantitySqMeter) || 0,
          rateFOB: parseFloat(item.rateFOB) || 0,
          amountFOB: parseFloat(item.amountFOB) || 0
        })) : [],

        // Financial details - from frontend service
        addedFreight: parseFloat(frontendData.addedFreight) || 0,
        insurance: parseFloat(frontendData.additionalCharges?.insurance) || 0,
        igstPercentage: parseFloat(frontendData.additionalCharges?.igstPercentage) || 0,
        igst: parseFloat(frontendData.additionalCharges?.igst) || 0,
        amountChargeableWords: frontendData.amountInWords || '',

        // Signature
        signature: frontendData.signature || '',

        // Calculated totals - from frontend service
        totalPieces: frontendData.totalPieces || 0,
        totalQuantity: frontendData.totalQuantity || 0,
        totalCifEuro: parseFloat(frontendData.totalFOB) || 0,
        grandTotal: parseFloat(frontendData.finalAmount) || 0,

        // System fields
        createdAt: new Date(),
        updatedAt: new Date()
      };

      console.log('Prepared invoice data for MongoDB:', JSON.stringify(invoiceData, null, 2));

      // Save to MongoDB using frontend invoice model
      const createdInvoice = new FrontendInvoice(invoiceData);
      const savedInvoice = await createdInvoice.save();

      console.log('✅ Invoice saved successfully with ID:', savedInvoice._id);
      console.log('✅ Invoice Number:', savedInvoice.invoiceNo);

      res.status(201).json({
        success: true,
        id: savedInvoice._id,
        invoiceNumber: savedInvoice.invoiceNo,
        message: 'Invoice created successfully',
        data: savedInvoice
      });

    } catch (error) {
      console.error('❌ CRITICAL ERROR creating export invoice:', error);
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      if (error.errors) {
        console.error('Validation errors:', error.errors);
      }

      // Ensure response is sent
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Error creating export invoice',
          error: error.message,
          details: error.errors || error,
          stack: error.stack
        });
      }
    }
  }

  // Get next invoice number with auto-increment (static method)
  async generateNextInvoiceNumberStatic() {
    try {
      // Find the latest invoice
      const latestInvoice = await FrontendInvoice.findOne().sort({ createdAt: -1 });

      let nextNumber = 755; // Starting number

      if (latestInvoice && latestInvoice.invoiceNo) {
        // Extract number from RE-XXX format
        const match = latestInvoice.invoiceNo.match(/RE-(\d+)/);
        if (match && match[1]) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }

      return `RE-${nextNumber}`;
    } catch (error) {
      console.error('Error generating invoice number:', error);
      return `RE-${Date.now()}`; // Fallback
    }
  }

  // Get next invoice number with auto-increment (instance method)
  async getNextInvoiceNumber() {
    return await this.generateNextInvoiceNumberStatic();
  }

  // Generate invoice number
  async generateInvoiceNumber(req, res) {
    try {
      // Get the latest invoice to determine the next number
      const latestInvoice = await Invoice.findOne().sort({ createdAt: -1 });
      
      let nextNumber = 100; // Default starting number
      
      if (latestInvoice) {
        // Extract the number part from the latest invoice number (format: RE-XXX)
        const match = latestInvoice.invoiceNo.match(/RE-(\d+)/);
        if (match && match[1]) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }
      
      // Format the invoice number with leading zeros if needed
      const invoiceNumber = `RE-${nextNumber}`;
      
      // Get current date in DD/MM/YYYY format
      const today = new Date();
      const day = String(today.getDate()).padStart(2, '0');
      const month = String(today.getMonth() + 1).padStart(2, '0'); // January is 0
      const year = today.getFullYear();
      const dateStr = `${day}/${month}/${year}`;
      
      res.status(200).json({ invoiceNumber: `${invoiceNumber} ${dateStr}` });
    } catch (error) {
      console.error('Error generating invoice number:', error);
      res.status(500).json({ message: 'Error generating invoice number', error: error.message });
    }
  }
}

module.exports = new ExportInvoiceController();
