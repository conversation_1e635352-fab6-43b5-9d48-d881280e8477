// Simple test server
console.log('🚀 Starting test server...');

try {
  const express = require('express');
  const cors = require('cors');
  
  const app = express();
  const PORT = 2000;
  
  app.use(cors());
  app.use(express.json());
  
  // Test route
  app.get('/test', (req, res) => {
    res.json({ message: 'Server is working!' });
  });
  
  // Login test route
  app.post('/api/phase-one/user/loginUser', (req, res) => {
    console.log('Login attempt:', req.body);
    res.json({ token: 'test-token', message: 'Login successful' });
  });
  
  app.listen(PORT, () => {
    console.log(`✅ Test server running on port ${PORT}`);
    console.log(`🌐 Test URL: http://localhost:${PORT}/test`);
    console.log(`🔐 Login URL: http://localhost:${PORT}/api/phase-one/user/loginUser`);
  });
  
} catch (error) {
  console.error('❌ Error starting server:', error);
}
