// Test frontend-based invoice creation
const axios = require('axios');

// Sample data exactly as frontend would send
const frontendInvoiceData = {
  // Basic invoice info
  invoiceNumber: 'RE-755', // Will be auto-generated
  invoiceDate: new Date(),
  exporterRef: 'EXP-TEST-001',
  exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
  
  // Buyer/Consignee info
  buyersOrderNo: 'BO-TEST-001',
  buyersOrderDate: new Date(),
  otherReferences: 'Other ref test',
  consignee: 'Test Customer\nTest Address\nGermany 12345',
  buyerIfOther: 'SAME',
  
  // Shipping details
  preCarriageBy: 'Road',
  placeOfReceipt: 'Bhadohi',
  originCountry: 'INDIA',
  destinationCountry: 'Germany',
  vesselNo: 'TEST-VESSEL-001',
  portOfLoading: 'Mumbai',
  portOfDischarge: 'Hamburg',
  finalDestination: 'Berlin',
  
  // Goods info
  marksAndNos: 'RACHIN-TEST-001',
  noOfKindOfPackage: '5 Bales',
  descriptionOfGoods: 'Indian Hand-Knotted Woolen Carpets',
  area: '507f1f77bcf86cd799439011',
  
  // Goods array
  goods: [
    {
      quality: 'Premium Quality',
      design: 'Traditional Design',
      pieces: 2,
      quantitySqMeter: 10.5,
      rateFOB: 150.00,
      amountFOB: 1575.00
    },
    {
      quality: 'Standard Quality',
      design: 'Modern Design',
      pieces: 3,
      quantitySqMeter: 15.0,
      rateFOB: 120.00,
      amountFOB: 1800.00
    }
  ],
  
  // Financial details
  addedFreight: 50.00,
  insurance: 25.00,
  igstPercentage: 18,
  igst: 607.50,
  amountChargeableWords: 'Four Thousand Fifty Seven Point Fifty Only',
  
  // Signature
  signature: 'Test Signature',
  
  // Calculated totals
  totalPieces: 5,
  totalQuantity: 25.5,
  totalCifEuro: 3375.00,
  grandTotal: 4057.50
};

console.log('🚀 Testing Frontend-based Invoice Creation...');
console.log('📤 Sending data to backend:', JSON.stringify(frontendInvoiceData, null, 2));

// Test the API
axios.post('http://localhost:2000/api/phase-four/export-invoice', frontendInvoiceData, {
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('\n✅ SUCCESS! Invoice created successfully!');
  console.log('📋 Response:', JSON.stringify(response.data, null, 2));
  console.log(`🎯 Invoice ID: ${response.data.id}`);
  console.log(`📄 Invoice Number: ${response.data.invoiceNumber}`);
  
  // Test getting the created invoice
  return axios.get(`http://localhost:2000/api/phase-four/export-invoice/${response.data.id}`);
})
.then(response => {
  console.log('\n✅ Invoice retrieval successful!');
  console.log('📋 Retrieved Invoice:', JSON.stringify(response.data, null, 2));
})
.catch(error => {
  console.error('\n❌ ERROR creating/retrieving invoice:');
  if (error.response) {
    console.error('Status:', error.response.status);
    console.error('Error data:', JSON.stringify(error.response.data, null, 2));
  } else {
    console.error('Error message:', error.message);
  }
});

// Test multiple invoice creation to check auto-increment
setTimeout(() => {
  console.log('\n🔄 Testing auto-increment with second invoice...');
  
  const secondInvoiceData = {
    ...frontendInvoiceData,
    exporterRef: 'EXP-TEST-002',
    buyersOrderNo: 'BO-TEST-002',
    goods: [
      {
        quality: 'Test Quality 2',
        design: 'Test Design 2',
        pieces: 1,
        quantitySqMeter: 5.0,
        rateFOB: 100.00,
        amountFOB: 500.00
      }
    ],
    totalPieces: 1,
    totalQuantity: 5.0,
    totalCifEuro: 500.00,
    grandTotal: 500.00,
    amountChargeableWords: 'Five Hundred Only'
  };
  
  axios.post('http://localhost:2000/api/phase-four/export-invoice', secondInvoiceData)
  .then(response => {
    console.log('\n✅ Second invoice created successfully!');
    console.log(`📄 Invoice Number: ${response.data.invoiceNumber}`);
    console.log('🎯 Auto-increment working correctly!');
  })
  .catch(error => {
    console.error('\n❌ Error creating second invoice:', error.response?.data || error.message);
  });
}, 2000);
