// Test with exact data structure that frontend service sends
const axios = require('axios');

// This is the exact structure that prepareInvoiceForBackend() method sends
const frontendServiceData = {
  exporter: {
    name: 'M/S Rachin Exports',
    address: 'Madhosingh, Santravidas Nagar, Dist Bhadohi, U.P. India'
  },
  consignee: {
    name: "507f1f77bcf86cd799439011", // Valid 24-character ObjectId
    address: 'Test Address, Germany',
    country: 'Germany'
  },
  invoiceNo: 'RE-755',
  invoiceDate: '15/01/2024',
  exportersRef: 'TEST-REF',
  buyerOrderNo: "507f1f77bcf86cd799439012", // Valid 24-character ObjectId
  buyerOrderDate: '10/01/2024',
  gstNo: '09**********1ZJ',
  panNo: '**********',
  shippingDetails: {
    preCarriageBy: 'Road',
    placeOfReceipt: 'Bhadohi',
    vesselNo: 'SEA-001',
    portOfLoading: 'Mumbai',
    countryOfOrigin: 'India',
    countryOfDestination: 'Germany',
    portOfDischarge: 'Hamburg',
    finalDestination: 'Germany',
    deliveryTerms: 'FOB'
  },
  goodsDescription: 'Indian Hand-Knotted Woolen Carpets',
  marksAndContNo: 'RACHIN-001',
  kindOfPkgs: 'Bales',
  rollNumbers: 'R001-R010',
  goodsDetails: [
    {
      quality: 'Test Quality',
      design: 'Test Design',
      pieces: 1,
      quantitySqMeter: 5.0,
      rateFOB: 100.00,
      amountFOB: 500.00
    }
  ],
  totalFOB: 500.00,
  woolPercentage: 80,
  cottonPercentage: 20,
  grossWeight: 100.00,
  netWeight: 95.00,
  additionalCharges: {
    insurance: 0,
    igst: 0,
    igstPercentage: 0
  },
  finalAmount: 500.00,
  amountInWords: 'Five Hundred Only',
  rexRegistrationNo: 'INREX1502000776DG015',
  declaration: "We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct."
};

console.log('🚀 Testing with Frontend Service Data Structure...');
console.log('📤 Sending data:', JSON.stringify(frontendServiceData, null, 2));

axios.post('http://localhost:2000/api/phase-four/export-invoice', frontendServiceData, {
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000
})
.then(response => {
  console.log('\n✅ SUCCESS! Invoice created with frontend service data!');
  console.log('📋 Response:', JSON.stringify(response.data, null, 2));
  console.log(`🎯 Invoice ID: ${response.data.id}`);
  console.log(`📄 Invoice Number: ${response.data.invoiceNumber}`);
})
.catch(error => {
  console.error('\n❌ ERROR:');
  if (error.response) {
    console.error('Status:', error.response.status);
    console.error('Data:', JSON.stringify(error.response.data, null, 2));
  } else if (error.request) {
    console.error('No response received');
  } else {
    console.error('Error:', error.message);
  }
});
