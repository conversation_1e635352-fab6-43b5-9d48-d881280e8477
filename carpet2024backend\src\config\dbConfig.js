const mongoose = require('mongoose');

const connect = async () => {
    try {
        console.log('🔄 Attempting to connect to MongoDB...');

        // MongoDB Atlas connection with optimized settings
        await mongoose.connect("mongodb+srv://infosarthaktech:<EMAIL>/test", {
            serverSelectionTimeoutMS: 60000, // 60 seconds timeout
            socketTimeoutMS: 60000, // 60 seconds timeout
            connectTimeoutMS: 60000, // 60 seconds connection timeout
            maxPoolSize: 10, // Maintain up to 10 socket connections
            minPoolSize: 5, // Maintain a minimum of 5 socket connections
            maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
            bufferMaxEntries: 0, // Disable mongoose buffering
            retryWrites: true, // Enable retryable writes
            w: 'majority' // Write concern
        });

        console.log("✅ Connected to MongoDB Atlas successfully");

        // Handle connection events
        mongoose.connection.on('error', (err) => {
            console.error('❌ MongoDB connection error:', err);
        });

        mongoose.connection.on('disconnected', () => {
            console.log('⚠️ MongoDB disconnected');
        });

        mongoose.connection.on('reconnected', () => {
            console.log('🔄 MongoDB reconnected');
        });

    } catch (error) {
        console.error("❌ Failed to connect to MongoDB:", error.message);

        // Try alternative connection method for local development
        console.log('🔄 Trying alternative connection...');
        try {
            await mongoose.connect("mongodb://localhost:27017/carpet_db", {
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 10000,
                connectTimeoutMS: 5000
            });
            console.log("✅ Connected to local MongoDB");
        } catch (localError) {
            console.error("❌ Local MongoDB also failed:", localError.message);
            console.log("⚠️ Running without database connection - some features may not work");
            // Don't throw error, let server start without DB
        }
    }
};

module.exports = connect;