// Simple test to check if backend is working
const axios = require('axios');

const testData = {
  invoiceNumber: 'RE-755',
  invoiceDate: new Date(),
  exporterRef: 'TEST-REF',
  exporter: 'M/S. RACHIN EXPORTS',
  buyersOrderNo: 'TEST-ORDER',
  buyersOrderDate: new Date(),
  consignee: 'Test Consignee',
  destinationCountry: 'Germany',
  descriptionOfGoods: 'Test Carpets',
  goods: [{
    quality: 'Test Quality',
    design: 'Test Design',
    pieces: 1,
    quantitySqMeter: 5,
    rateFOB: 100,
    amountFOB: 500
  }],
  totalPieces: 1,
  totalQuantity: 5,
  totalCifEuro: 500,
  grandTotal: 500,
  amountChargeableWords: 'Five Hundred Only'
};

console.log('🔄 Testing backend connection...');
console.log('📤 Sending test data to: http://localhost:2000/api/phase-four/export-invoice');

axios.post('http://localhost:2000/api/phase-four/export-invoice', testData, {
  headers: { 'Content-Type': 'application/json' }
})
.then(response => {
  console.log('✅ SUCCESS! Backend is working');
  console.log('📥 Response:', response.data);
  console.log('🆔 Invoice ID:', response.data.id);
  console.log('🔢 Invoice Number:', response.data.invoiceNumber);
})
.catch(error => {
  console.log('❌ ERROR! Backend connection failed');
  if (error.response) {
    console.log('📊 Status:', error.response.status);
    console.log('📄 Error Data:', error.response.data);
  } else if (error.request) {
    console.log('🌐 No response from server');
    console.log('📡 Request:', error.request);
  } else {
    console.log('⚠️ Error:', error.message);
  }
});
