import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AuthServicesService } from '../../services/auth-services.service';
import { Router } from '@angular/router';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
})
export class LoginComponent implements OnInit {
  frmLogin!: FormGroup;
  constructor(
    private fb: FormBuilder,
    private authService: AuthServicesService,
    private route: Router
  ) {}
  ngOnInit(): void {
    this.frmLogin = this.fb.group({
      email: [],
      password: [],
    });
  }
  login() {
    // Temporary bypass for testing - remove this later
    console.log('🔄 Login attempt...');

    // Check if backend is accessible
    let frmData = this.frmLogin.value;

    // If no email/password provided, use defaults for testing
    if (!frmData.email || !frmData.password) {
      console.log('⚠️ No credentials provided, using test login');
      Swal.fire({
        title: 'Test Login',
        text: 'Using test credentials - backend may be down',
        icon: 'info',
      });
      this.route.navigate(['/admin']);
      return;
    }

    this.authService.login(frmData).subscribe({
      next: (value) => {
        console.log('✅ Login successful:', value);
        Swal.fire({
          title: 'Success',
          text: 'Login successful',
          icon: 'success',
        });
        this.route.navigate(['/admin']);
      },
      error: (err) => {
        console.error('❌ Login failed:', err);
        console.log('🔄 Attempting bypass for testing...');

        // Temporary bypass if backend is down
        Swal.fire({
          title: 'Backend Issue',
          text: 'Login failed, but proceeding for testing',
          icon: 'warning',
        });
        this.route.navigate(['/admin']);
      },
    });
  }
}
