const express = require('express');

const router = express.Router();
const buyerOrder=require('../phase-4/buyerOrder-routes');

const carpetOrderIssue=require('../phase-4/carpetOrderIssue-routes');
const CarpetReceived=require('../phase-4/carpetRecevied-routes');
const carpetReceivedUpdates=require('../phase-4/carpetReceivedUpdates-routes');
const purchaseDetails = require('../phase-4/purchase-routes');
const materialDeying = require('../phase-4/materialDeying-routes');
const issueNo = require('../phase-4/issueNo-routes')
const invoice = require('../phase-4/invoice-routes');
const exportPackingList = require('./exportPacking-routes');
const exportInvoice = require('./export-invoice-routes');
const CRecievedOrderListDtRoutes = require('../phase-4/CRecievedOrderListDt-routes');

// Import buyer controller from phase-3 for fast access
const buyerController = require('../../controller/masterBuyer-controller');


router.use('/invoice', invoice);
router.use('/issueno', issueNo)
router.use('/materialDeying',materialDeying);
router.use('/purchase',purchaseDetails);
router.use('/carpetReceived',CarpetReceived);
router.use('/',carpetReceivedUpdates);
router.use('/carpetOrder',carpetOrderIssue);
router.use('/buyerOrder',buyerOrder);
router.use('/exportPackingList', exportPackingList);
router.use('/export-invoice', exportInvoice); // Added missing export-invoice route

// Add fast buyer route for consignee dropdown
router.get('/buyer', buyerController.getAllBuyers);

module.exports = router;
