const mongoose = require('mongoose');

// Frontend Invoice Schema - Exact mapping from Angular form
const frontendInvoiceSchema = new mongoose.Schema({
  // Basic invoice info
  invoiceNo: { type: String, required: true, unique: true }, // Auto-generated RE-XXX
  invoiceDate: { type: String, required: true }, // DD/MM/YYYY format
  exportersRef: { type: String, default: '' },
  
  // Exporter info (fixed)
  exporter: { type: String, default: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA' },
  
  // Buyer/Consignee info
  buyersOrderNo: { type: String, default: '' },
  buyersOrderDate: { type: String, default: '' }, // DD/MM/YYYY format
  otherReferences: { type: String, default: '' },
  consignee: { type: String, default: '' }, // Text field as in frontend
  buyerIfOther: { type: String, default: 'SAME' },
  
  // Shipping details
  preCarriageBy: { type: String, default: '' },
  placeOfReceipt: { type: String, default: '' },
  originCountry: { type: String, default: 'INDIA' },
  destinationCountry: { type: String, default: '' },
  vesselNo: { type: String, default: '' },
  portOfLoading: { type: String, default: '' },
  portOfDischarge: { type: String, default: '' },
  finalDestination: { type: String, default: '' },
  
  // Goods info
  marksAndNos: { type: String, default: '' },
  noOfKindOfPackage: { type: String, default: '' },
  descriptionOfGoods: { type: String, default: 'Indian Hand-Knotted Woolen Carpets' },
  area: { type: String, default: null }, // Can be ObjectId string or null
  
  // Goods array - exactly as frontend FormArray
  goods: [{
    quality: { type: String, default: '' },
    design: { type: String, default: '' },
    pieces: { type: Number, default: 0 },
    quantitySqMeter: { type: Number, default: 0 },
    rateFOB: { type: Number, default: 0 },
    amountFOB: { type: Number, default: 0 }
  }],
  
  // Financial details
  addedFreight: { type: Number, default: 0 },
  insurance: { type: Number, default: 0 },
  igstPercentage: { type: Number, default: 0 },
  igst: { type: Number, default: 0 },
  amountChargeableWords: { type: String, default: '' },
  
  // Signature
  signature: { type: String, default: '' },
  
  // Calculated totals (computed in frontend)
  totalPieces: { type: Number, default: 0 },
  totalQuantity: { type: Number, default: 0 },
  totalCifEuro: { type: Number, default: 0 },
  grandTotal: { type: Number, default: 0 },
  
  // System fields
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true, // Automatically manage createdAt and updatedAt
  collection: 'frontend_invoices' // Custom collection name
});

// Index for faster queries
frontendInvoiceSchema.index({ invoiceNo: 1 });
frontendInvoiceSchema.index({ createdAt: -1 });

// Pre-save middleware to update the updatedAt field
frontendInvoiceSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for formatted invoice number
frontendInvoiceSchema.virtual('formattedInvoiceNo').get(function() {
  return this.invoiceNo;
});

// Method to get next invoice number
frontendInvoiceSchema.statics.getNextInvoiceNumber = async function() {
  try {
    const latestInvoice = await this.findOne().sort({ createdAt: -1 });
    
    let nextNumber = 755; // Starting number
    
    if (latestInvoice && latestInvoice.invoiceNo) {
      const match = latestInvoice.invoiceNo.match(/RE-(\d+)/);
      if (match && match[1]) {
        nextNumber = parseInt(match[1], 10) + 1;
      }
    }
    
    return `RE-${nextNumber}`;
  } catch (error) {
    console.error('Error generating invoice number:', error);
    return `RE-${Date.now()}`;
  }
};

// Method to calculate totals
frontendInvoiceSchema.methods.calculateTotals = function() {
  this.totalPieces = this.goods.reduce((sum, item) => sum + (item.pieces || 0), 0);
  this.totalQuantity = this.goods.reduce((sum, item) => sum + (item.quantitySqMeter || 0), 0);
  this.totalCifEuro = this.goods.reduce((sum, item) => sum + (item.amountFOB || 0), 0);
  this.grandTotal = this.totalCifEuro + (this.addedFreight || 0) + (this.insurance || 0) + (this.igst || 0);
  return this;
};

// Export the model
module.exports = mongoose.model('FrontendInvoice', frontendInvoiceSchema);
