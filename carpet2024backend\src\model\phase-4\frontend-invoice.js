const mongoose = require('mongoose');

const frontendInvoiceSchema = new mongoose.Schema({
  // Auto-generated invoice number with unique constraint
  invoiceNo: {
    type: String,
    unique: true,
    required: true
  },
  
  // Basic invoice details
  invoiceDate: {
    type: Date,
    default: Date.now
  },
  exporterRef: String,
  exporter: mongoose.Schema.Types.Mixed, // Can be string or object
  buyersOrderNo: String,
  buyersOrderDate: Date,
  otherReferences: String,
  consignee: mongoose.Schema.Types.Mixed, // Can be string or object
  buyerIfOther: String,
  
  // Shipping details
  preCarriageBy: String,
  placeOfReceipt: String,
  originCountry: {
    type: String,
    default: 'INDIA'
  },
  destinationCountry: String,
  vesselNo: String,
  portOfLoading: String,
  portOfDischarge: String,
  finalDestination: String,
  
  // Package and goods details
  marksAndNos: String,
  noOfKindOfPackage: String,
  descriptionOfGoods: {
    type: String,
    default: 'Indian Hand-Knotted Woolen Carpets'
  },
  area: String, // Reference to SizeMaster
  
  // Goods array
  goods: [{
    quality: String,
    design: String,
    pieces: {
      type: Number,
      default: 0
    },
    quantitySqMeter: {
      type: Number,
      default: 0
    },
    rateFOB: {
      type: Number,
      default: 0
    },
    amountFOB: {
      type: Number,
      default: 0
    }
  }],
  
  // Financial details
  addedFreight: {
    type: Number,
    default: 0
  },
  insurance: {
    type: Number,
    default: 0
  },
  igstPercentage: {
    type: Number,
    default: 0
  },
  igst: {
    type: Number,
    default: 0
  },
  
  // Totals
  totalPieces: {
    type: Number,
    default: 0
  },
  totalQuantity: {
    type: Number,
    default: 0
  },
  totalCifEuro: {
    type: Number,
    default: 0
  },
  grandTotal: {
    type: Number,
    default: 0
  },
  
  // Additional fields
  amountChargeableWords: String,
  signature: String,
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index for faster queries (invoiceNo already has unique index from schema)
frontendInvoiceSchema.index({ createdAt: -1 });

// Update the updatedAt field before saving
frontendInvoiceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('FrontendInvoice', frontendInvoiceSchema, 'export_invoices');
